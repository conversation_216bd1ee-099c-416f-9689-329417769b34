.leaderboard-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.leaderboard-modal-container {
  background: white;
  border-radius: 16px;
  width: 50%;
  max-width: 800px;
  max-height: 90vh;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
  transform: scale(0.9);
  animation: modalSlideIn 0.3s ease-out forwards;
}

@keyframes modalSlideIn {
  to {
    transform: scale(1);
  }
}

.leaderboard-modal-header {
  background: linear-gradient(135deg, #FF9800, #FF5722);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.leaderboard-modal-close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.leaderboard-modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.leaderboard-modal-content {
  padding: 30px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Loading and error states */
.leaderboard-loading,
.leaderboard-error,
.leaderboard-no-data {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #FF9800;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.leaderboard-error {
  color: #f44336;
}

.leaderboard-no-data {
  color: #666;
}

/* Table styles */
.leaderboard-table-container {
  overflow-x: auto;
}

.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.leaderboard-table th {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #333;
  font-weight: 600;
  padding: 15px 12px;
  text-align: left;
  border-bottom: 2px solid #dee2e6;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leaderboard-table td {
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.leaderboard-table tbody tr:hover {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.placement-cell {
  font-weight: 700;
  color: #FF9800;
  text-align: center;
  width: 80px;
}

.rank-cell {
  text-align: center;
  width: 80px;
}

.rank-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: center;
}

.nickname-cell {
  font-weight: 600;
  color: #333;
}

.winrate-cell {
  text-align: center;
  font-weight: 500;
  width: 100px;
}

.percentile-cell {
  text-align: center;
  font-weight: 500;
  color: #666;
  width: 100px;
}

/* Responsive design */
@media (max-width: 768px) {
  .leaderboard-modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .leaderboard-modal-header {
    padding: 15px 20px;
  }

  .leaderboard-modal-header h2 {
    font-size: 1.3rem;
  }

  .leaderboard-modal-content {
    padding: 0px;
  }

  .leaderboard-table th,
  .leaderboard-table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .rank-badge {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .leaderboard-table th,
  .leaderboard-table td {
    padding: 6px 4px;
    font-size: 0.8rem;
  }

  .placement-cell,
  .rank-cell,
  .winrate-cell,
  .percentile-cell {
    width: auto;
  }
}
