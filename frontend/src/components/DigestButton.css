

.digest-button {
  background: linear-gradient(135deg, #673AB7, #9C27B0);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(103, 58, 183, 0.3);
  white-space: nowrap;
  min-width: 200px;
  justify-content: center;
}

.digest-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(103, 58, 183, 0.4);
}

.digest-button:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.digest-button.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.digest-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .digest-button {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 180px;
  }
  
  .digest-button-container {
    margin: 20px 0 15px 0;
  }
}
