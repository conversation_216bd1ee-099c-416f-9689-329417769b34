/* Basic reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  padding: 30px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.App {
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px 15px 30px 15px; /* Added horizontal padding */
  background-color: #f5f5f5;
}

.app-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10px;
  gap: 10px;
}

h2 {
  margin-bottom: 15px;
  color: #444;
}

/* Main container with flexible layout */
.main-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "players teams"
    "balancing-status teams";
  gap: 30px;
  padding: 10px;
  margin-top: 10px; /* Added top margin to compensate for removed title */
  /* Removed min-height to allow natural sizing */
}

/* Players section */
.players-section {
  grid-area: players;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content; /* Allow natural height */
}

/* Teams section */
.teams-section {
  grid-area: teams;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content; /* Allow natural height */
}

/* Balancing status section */
.balancing-status-section {
  grid-area: balancing-status;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: fit-content;
  animation: fadeIn 0.5s ease-in-out;
}

/* Buttons container for digest and leaderboard */
.buttons-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0 20px 0;
  padding: 0 10px;
  flex-wrap: wrap;
}

/* Responsive layout */
@media (max-width: 768px) {
  .main-container {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-areas:
      "players"
      "teams"
      "balancing-status";
    gap: 30px;
  }

  body {
    padding: 20px;
  }

  .App {
    padding: 0 10px 20px 10px;
  }

  .buttons-container {
    gap: 15px;
    margin: 20px 0 15px 0;
  }
}

/* Spinner Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.refresh-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 4px;
  vertical-align: middle;
}

.refresh-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 3px;
  width: 80px;
  height: 28px;
  transition: background-color 0.3s;
}

.refresh-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.refresh-button:not(:disabled):hover {
  background-color: #1976D2;
}

.ios-mine-bubble {
      color: black;
      background: rgb(230, 230, 230);
      border-radius: 20px; /* iOS uses a rounder radius */
      position: relative; /* Essential for the tail positioning */
    }

    /* Tail Part 1: The main blue shape */
    .ios-mine-bubble::before {
      content: "";
      position: absolute;
      z-index: 0;
      bottom: 0;
      right: -8px; /* Position right outside the bubble */
      height: 20px;
      width: 20px;
      background: rgb(230, 230, 230);
      border-bottom-left-radius: 15px; /* The crucial curve */
    }

    /* Tail Part 2: The 'cutout' (Assumes white background) */
    .ios-mine-bubble::after {
      content: "";
      position: absolute;
      z-index: 1;
      bottom: 0;
      right: -10px; /* Position slightly further out */
      width: 10px;
      height: 20px;
      /* IMPORTANT: This MUST match your page/container background */
      background: white;
      border-bottom-left-radius: 10px; /* Curve to match */
    }

/* Animation for fade in effect - kept for potential future use */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Animation for teams overlay text sliding up from bottom */
@keyframes slideUpFromBottom {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Message bubble slide-in animation */
@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-2px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Dimming overlay styles */
.dim-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999; /* Lower than teams-container */
  cursor: pointer;
  animation: fadeIn 0.5s ease-out;
}

.dim-overlay-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 18px;
  text-align: center;
  pointer-events: none;
  opacity: 0.8;
}

/* Ensure teams-container has higher z-index to stay above dimming */
.teams-section {
  position: relative;
  z-index: 1000;
}
