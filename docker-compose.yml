services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      platforms:
        - "linux/amd64"
        - "linux/arm64"
    container_name: team-balancer-backend-prod
    image: ${REGISTRY_URL}/teammates-suck-backend:${BUILD_TAG}
    restart: unless-stopped
    ports:
      - "5050:5050"
    environment:
      - PYTHONPATH=/app/src
      - FLASK_APP=src/app.py
      - PYTHONUNBUFFERED=1
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - SPREADSHEET_ID=${SPREADSHEET_ID}
      - DB_PATH=data/database.sqlite
      - DIGEST_PATH=data/digest
      - APP_SCORES=${SCORES}
    volumes:
      - db:/app/data

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      platforms:
        - "linux/amd64"
        - "linux/arm64"
      args:
        - VITE_API_BASE_URL=/api
        - VITE_BUILD_TAG=${BUILD_TAG}
        - VITE_APP_SCORES=${SCORES}
    container_name: team-balancer-frontend-prod
    image: ${REGISTRY_URL}/teammates-suck-frontend:${BUILD_TAG}
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend

  sqlite-viewer:
    image: nikitagordia/sqlite-web:latest
    container_name: team-balancer-sqlite-viewer-prod
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - db:/db_data
    environment:
      - SQLITE_WEB_USERNAME=${WEB_DB_USERNAME}
      - SQLITE_WEB_PASSWORD=${WEB_DB_PASSWORD}
    command: ["sqlite_web", "--port=8080",
                            "--host=0.0.0.0",
                            "--no-browser",
                            "--read-only",
                            "--password",
                            "/db_data/database.sqlite"]
    depends_on:
      - backend
    networks:
      - default
networks:
  default:
    name: team-balancer-network-prod

volumes:
  db: